package pubsub

import (
	"context"

	commonPubsub "fancygame/tasrv/pkg/pubsub"
	"github.com/sirupsen/logrus"
)

// ConsumerService wraps the common pubsub service for this specific service
type ConsumerService struct {
	*commonPubsub.KafkaConsumerService
}

// TopicConfig is an alias for the common TopicConfig
type TopicConfig = commonPubsub.TopicConfig

// NewTopic creates a new topic configuration
func NewTopic(topic string, handler func([]byte)) *TopicConfig {
	// Adapt the old handler signature to the new one
	adaptedHandler := func(ctx context.Context, data []byte) error {
		handler(data)
		return nil
	}
	return commonPubsub.NewTopicConfig(topic, adaptedHandler)
}

// NewConsumerService creates a new consumer service using the common library
func NewConsumerService(topicsConfig []*TopicConfig) *ConsumerService {
	kafkaService, err := commonPubsub.NewKafkaConsumerService(
		topicsConfig,
		commonPubsub.WithShutdownTimeout(10),
		commonPubsub.WithBufferSize(len(topicsConfig)),
	)
	if err != nil {
		logrus.Fatalf("Failed to create consumer service: %v", err)
	}

	return &ConsumerService{
		KafkaConsumerService: kafkaService,
	}
}

// MessageProducer is an alias for the common Producer interface
type MessageProducer = commonPubsub.Producer

// InitProducer initializes the producer using the common library
func InitProducer() {
	if err := commonPubsub.InitKafkaProducer(); err != nil {
		logrus.Fatalf("Failed to initialize producer: %v", err)
	}
}

// GetProducer returns the default producer instance
func GetProducer() MessageProducer {
	producer, err := commonPubsub.GetDefaultProducer()
	if err != nil {
		logrus.Fatalf("Failed to get producer: %v", err)
	}
	return producer
}
