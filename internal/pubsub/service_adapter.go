package pubsub

import (
	"context"

	"github.com/sirupsen/logrus"
	commonPubsub "fancygame/tasrv/pkg/pubsub"
)

// ConsumerService 包装公共pubsub服务
type ConsumerService struct {
	*commonPubsub.ConsumerService
}

// TopicConfig 主题配置别名（保持向后兼容）
type TopicConfig = commonPubsub.TopicRegistration

// NewTopic 创建新的主题配置（保持向后兼容）
func NewTopic(topic string, handler func([]byte)) *TopicConfig {
	// 适配旧的处理函数签名到新的签名
	adaptedHandler := func(ctx context.Context, data []byte) error {
		handler(data)
		return nil
	}
	return commonPubsub.RegisterTopic(topic, adaptedHandler)
}

// NewConsumerService 使用公共库创建新的消费者服务
func NewConsumerService(topicsConfig []*TopicConfig) *ConsumerService {
	kafkaService, err := commonPubsub.NewKafkaConsumerService(
		topicsConfig,
		commonPubsub.WithShutdownTimeout(10),
		commonPubsub.WithBufferSize(len(topicsConfig)),
	)
	if err != nil {
		logrus.Fatalf("Failed to create consumer service: %v", err)
	}

	return &ConsumerService{
		ConsumerService: kafkaService,
	}
}

// MessageProducer 消息生产者接口别名
type MessageProducer = commonPubsub.MessageProducer

// InitProducer 使用公共库初始化生产者
func InitProducer() {
	if err := commonPubsub.InitKafkaProducer(); err != nil {
		logrus.Fatalf("Failed to initialize producer: %v", err)
	}
}

// GetProducer 返回默认生产者实例
func GetProducer() MessageProducer {
	producer, err := commonPubsub.GetDefaultProducer()
	if err != nil {
		logrus.Fatalf("Failed to get producer: %v", err)
	}
	return producer
}
