package pubsub

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"
)

// ExampleGracefulShutdown 演示优雅关闭的使用方式
func ExampleGracefulShutdown() {
	// 创建一些测试主题配置
	topics := []*TopicConfig{
		NewTopic("test_topic_1", func(data []byte) {
			// 模拟消息处理
			time.Sleep(100 * time.Millisecond)
			logrus.Infof("处理消息1: %s", string(data))
		}),
		NewTopic("test_topic_2", func(data []byte) {
			// 模拟消息处理
			time.Sleep(150 * time.Millisecond)
			logrus.Infof("处理消息2: %s", string(data))
		}),
		NewTopic("test_topic_3", func(data []byte) {
			// 模拟消息处理
			time.Sleep(200 * time.Millisecond)
			logrus.Infof("处理消息3: %s", string(data))
		}),
	}

	// 创建消费者服务
	consumerService := NewConsumerService(topics)

	// 启动服务
	ctx := context.Background()
	logrus.Info("启动消费者服务...")
	consumerService.Start(ctx)

	// 模拟运行一段时间
	logrus.Info("服务运行中，模拟处理消息...")
	time.Sleep(3 * time.Second)

	// 开始优雅关闭
	logrus.Info("开始优雅关闭流程...")
	consumerService.Stop()

	logrus.Info("示例完成")
}

// ExampleWithSignalHandling 演示结合信号处理的优雅关闭
func ExampleWithSignalHandling() {
	topics := []*TopicConfig{
		NewTopic("signal_test_topic", func(data []byte) {
			logrus.Infof("处理消息: %s", string(data))
		}),
	}

	consumerService := NewConsumerService(topics)
	ctx := context.Background()

	// 启动服务
	logrus.Info("启动消费者服务...")
	consumerService.Start(ctx)

	// 模拟接收到关闭信号
	logrus.Info("模拟接收到关闭信号...")
	time.Sleep(1 * time.Second)

	// 优雅关闭
	logrus.Info("执行优雅关闭...")
	consumerService.Stop()

	logrus.Info("服务已完全关闭")
}
