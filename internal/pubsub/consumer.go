package pubsub

//
//import (
//	"context"
//	"time"
//
//	commonPubsub "tasrv/pkg/pubsub"
//	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
//	"github.com/sirupsen/logrus"
//)
//
//type consumerWrapper struct {
//	receiver event.Receiver
//	handler  event.Handler
//	topic    string
//}
//
//func newConsumerWrapper(topic string) *consumerWrapper {
//	r, _ := event.NewKafkaReceiver(
//		event.WithTopic(topic),
//	)
//	return &consumerWrapper{
//		receiver: r,
//		topic:    topic,
//	}
//}
//
//func (c *consumerWrapper) Start(ctx context.Context) error {
//	return c.receiver.SafeReceive(ctx, c.handler)
//}
//
//type ConsumerService struct {
//	consumers []*consumerWrapper
//	ctx       context.Context
//	cancel    context.CancelFunc
//	doneCh    chan struct{} // 用于接收每个消费者完成的信号
//}
//
//func NewConsumerService(topicsConfig []*TopicConfig) *ConsumerService {
//	ctx, cancel := context.WithCancel(context.Background())
//	cs := &ConsumerService{
//		ctx:    ctx,
//		cancel: cancel,
//		doneCh: make(chan struct{}, len(topicsConfig)), // 缓冲管道，容量等于消费者数量
//	}
//
//	for _, cfg := range topicsConfig {
//		wrapper := newConsumerWrapper(cfg.Topic)
//		config := cfg
//		wrapper.handler = func(ctx context.Context, evt event.Event) error {
//			config.Handler(evt.Value())
//			logrus.Tracef("[%s] consume key:%s", config.Topic, evt.Key())
//			return nil
//		}
//		cs.consumers = append(cs.consumers, wrapper)
//	}
//	return cs
//}
//
//func (cs *ConsumerService) Start(ctx context.Context) {
//	for _, c := range cs.consumers {
//		go func(w *consumerWrapper) {
//			defer func() {
//				// 执行完后写入关闭的管道
//				cs.doneCh <- struct{}{}
//				logrus.Infof("Consumer [%s] execution completed", w.topic)
//			}()
//
//			if err := w.Start(cs.ctx); err != nil {
//				if cs.ctx.Err() == nil {
//					logrus.Errorf("Consumer [%s] error: %v", w.topic, err)
//				} else {
//					logrus.Infof("Consumer [%s] stopped due to shutdown signal", w.topic)
//				}
//			}
//		}(c)
//	}
//
//	logrus.Infof("Started %d kafka consumers", len(cs.consumers))
//}
//
//func (cs *ConsumerService) Stop() {
//	logrus.Info("Starting graceful shutdown of consumer service...")
//
//	// 1. 取消context，停止接收新消息
//	cs.cancel()
//	logrus.Info("Stop signal sent")
//
//	// 2. 等待所有消费者执行完成
//	cs.waitForConsumersToComplete()
//
//	// 3. 关闭所有receiver
//	cs.closeAllReceivers()
//
//	logrus.Info("Consumer service shutdown completed")
//}
//
//// waitForConsumersToComplete 等待所有消费者完成执行
//func (cs *ConsumerService) waitForConsumersToComplete() {
//	consumerCount := len(cs.consumers)
//	completedCount := 0
//
//	logrus.Infof("Waiting for %d consumers to complete execution...", consumerCount)
//
//	for completedCount < consumerCount {
//		select {
//		case <-cs.doneCh:
//			completedCount++
//			logrus.Infof("Completed %d/%d consumers", completedCount, consumerCount)
//		case <-time.After(10 * time.Second):
//			logrus.Warnf("Timeout waiting for consumers to complete, %d/%d completed, forcing exit", completedCount, consumerCount)
//			return
//		}
//	}
//
//	logrus.Info("All consumers completed execution")
//}
//
//// closeAllReceivers 关闭所有receiver
//func (cs *ConsumerService) closeAllReceivers() {
//	for _, c := range cs.consumers {
//		if err := c.receiver.Close(); err != nil {
//			logrus.Errorf("Failed to close receiver [%s]: %v", c.topic, err)
//		}
//	}
//}
//
//// IsRunning 检查服务是否还在运行
//func (cs *ConsumerService) IsRunning() bool {
//	select {
//	case <-cs.ctx.Done():
//		return false
//	default:
//		return true
//	}
//}
