package pubsub

import (
	"context"
	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"github.com/sirupsen/logrus"
	"time"
)

type consumerWrapper struct {
	receiver event.Receiver
	handler  event.Handler
	topic    string
}

func newConsumerWrapper(topic string) *consumerWrapper {
	r, _ := event.NewKafkaReceiver(
		event.WithTopic(topic),
	)
	return &consumerWrapper{
		receiver: r,
		topic:    topic,
	}
}

func (c *consumerWrapper) Start(ctx context.Context) error {
	return c.receiver.SafeReceive(ctx, c.handler)
}

type ConsumerService struct {
	consumers []*consumerWrapper
	ctx       context.Context
	cancel    context.CancelFunc
}

func NewConsumerService(topicsConfig []*TopicConfig) *ConsumerService {
	ctx, cancel := context.WithCancel(context.Background())
	cs := &ConsumerService{
		ctx:    ctx,
		cancel: cancel,
	}

	for _, cfg := range topicsConfig {
		wrapper := newConsumerWrapper(cfg.Topic)
		config := cfg
		wrapper.handler = func(ctx context.Context, evt event.Event) error {
			config.Handler(evt.Value())
			logrus.Tracef("[%s] consume key:%s", config.Topic, evt.Key())
			return nil
		}
		cs.consumers = append(cs.consumers, wrapper)
	}
	return cs
}

func (cs *ConsumerService) Start(ctx context.Context) {
	for _, c := range cs.consumers {
		go func(w *consumerWrapper) {
			// 执行完后写入关闭的管道
			if err := w.Start(cs.ctx); err != nil {
				if cs.ctx.Err() == nil {
					logrus.Errorf("Consumer [%s] error: %v", w.topic, err)
				} else {
					logrus.Infof("Consumer [%s] stopped due to shutdown signal", w.topic)
				}
			}
		}(c)
	}

	logrus.Infof("Started %d kafka consumers", len(cs.consumers))
}

func (cs *ConsumerService) Stop() {
	logrus.Info("开始优雅关闭消费者服务...")

	// 1. 取消context，停止接收新消息
	cs.cancel()
	logrus.Info("已发送停止信号")

	// 收到所有执行完的管道后退出
	stoplist == len(cs.consumers)

	// 2. 等待5秒让正在处理的消息完成
	time.AfterFunc(5*time.Second, func() {
		for _, c := range cs.consumers {
			if err := c.receiver.Close(); err != nil {
				logrus.Errorf("关闭receiver [%s] 失败: %v", c.topic, err)
			}
		}
	})

	logrus.Info("消费者服务已关闭")
}

// IsRunning 检查服务是否还在运行
func (cs *ConsumerService) IsRunning() bool {
	select {
	case <-cs.ctx.Done():
		return false
	default:
		return true
	}
}
