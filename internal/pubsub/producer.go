package pubsub

//
//import (
//	"context"
//	"fmt"
//	"sync"
//
//	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
//	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
//	"github.com/sirupsen/logrus"
//)
//
//// MessageProducer 定义了消息生产者的接口
//type MessageProducer interface {
//	Produce(ctx context.Context, topic string, key string, value []byte) error
//}
//
//// kafkaProducer 实现了MessageProducer接口，用于向Kafka发送消息
//type kafkaProducer struct {
//	sender event.Sender
//}
//
//var (
//	producer     MessageProducer
//	producerOnce sync.Once
//)
//
//// InitProducer 初始化消息生产者，确保只初始化一次
//func InitProducer() {
//	producerOnce.Do(func() {
//		sender, err := event.NewKafkaSender()
//		if err != nil {
//			logrus.Fatalf("创建Kafka发送器失败: %s", err)
//		}
//		producer = &kafkaProducer{sender: sender}
//	})
//}
//
//// GetProducer 返回初始化后的消息生产者实例
//func GetProducer() MessageProducer{
//	return producer
//}
//
//// Produce 向指定的Kafka主题发送消息
//func (p *kafkaProducer) Produce(ctx context.Context, topic string, key string, value []byte) error {
//	entry := logx.NewLogEntry(ctx)
//	entry.Infof("向Kafka发送消息: Topic=%s, Key=%s, 大小=%d字节", topic, key, len(value))
//
//	err := p.sender.SendWithTopic(ctx, topic, event.NewMessage(key, value))
//	if err != nil {
//		return fmt.Errorf("发送消息到Kafka失败: %w", err)
//	}
//
//	return nil
//}
