package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_login"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	"tasrv/internal/dao"
	"tasrv/internal/model"
	"tasrv/internal/services"
	"time"
)

func ProcessLoginMessage(data []byte) {
	logrus.Printf("ProcessLoginMessage: %s", string(data))

	var teLoginInfo a_login.TeLoginInfo
	err := json.Unmarshal(data, &teLoginInfo)
	if err != nil {
		logrus.Errorf("ProcessLoginMessage json.Unmarshal error, data:%+v, err:%s", data, err)
		return
	}

	// 转换上报信息
	record := model.TeLoginRecord{
		DefaultHeader: teLoginInfo.DefaultHeader,
		TeLogin:       teLoginInfo.TeLogin,
	}
	playerId := cast.ToString(teLoginInfo.PlayerId)
	// 记录登录时间
	err = new(dao.Tracker).Login(context.Background(), playerId, record.LastLoginTime)
	if err != nil {
		logrus.Errorf("ProcessLoginMessage dao login error, record:%+v, err:%s", record, err)
	}

	services.GTeService.Track(playerId, teLoginInfo.GetEvent(), &record, teLoginInfo.LastLoginTime.Unix())
}

func ProcessLoginMessageTest(data []byte) {
	logrus.Printf("ProcessLoginMessageTest: %s", string(data))
	ctx := context.Background()
	key := "safetest:duplicate:check:" + string(data)

	// 尝试设置key，如果key已存在则返回false
	result, err := redisx.GetPlayerCli().SetNX(ctx, key, "processed", time.Hour).Result()
	if err != nil {
		logrus.Errorf("ProcessLoginMessageTest redis error: %v", err)
		panic(fmt.Sprintf("Redis操作失败: %v", err))
	}

	if !result {
		// key已存在，说明重复消费了
		logrus.Errorf("ProcessLoginMessageTest 检测到重复消费! data: %s", string(data))
		panic(fmt.Sprintf("重复消费检测: 数据 %s 已经被处理过了!", string(data)))
	}

	time.Sleep(5 * time.Second)

	logrus.Printf("ProcessLoginMessageTest 首次处理数据: %s", string(data))
}

func ProcessRegisterMessage(data []byte) {
	logrus.Printf("ProcessRegisterMessage: %s", string(data))

	var teRegisterInfo a_login.TeRegisterInfo
	err := json.Unmarshal(data, &teRegisterInfo)
	if err != nil {
		logrus.Errorf("ProcessRegisterMessage json.Unmarshal error, data:%+v, err:%s", data, err)
		return
	}

	playerId := cast.ToString(teRegisterInfo.PlayerId)

	// 转换上报信息
	record := model.TeRegisterRecord{
		DefaultHeader: teRegisterInfo.DefaultHeader,
		TeRegister:    teRegisterInfo.TeRegister,
	}

	services.GTeService.UserSetOnce(playerId, &record)
	services.GTeService.Track(playerId, teRegisterInfo.GetEvent(), &record, teRegisterInfo.RegisterTime.Unix())
}

func ProcessLogoutMessage(data []byte) {
	logrus.Printf("ProcessLogoutMessage: %s", string(data))

	var teLogoutInfo a_login.TeLogoutInfo
	err := json.Unmarshal(data, &teLogoutInfo)
	if err != nil {
		logrus.Errorf("ProcessLogoutMessage json.Unmarshal error, data:%+v, err:%s", data, err)
		return
	}

	playerId := cast.ToString(teLogoutInfo.PlayerId)

	onlineTime, err := new(dao.Tracker).Logout(context.Background(), playerId, teLogoutInfo.LogoutTime)
	if err != nil {
		logrus.Errorf("ProcessLogoutMessage dao logout error, teLogoutInfo:%+v, err:%s", teLogoutInfo, err)
	}
	// 转换上报信息
	record := model.TeLogoutRecord{
		DefaultHeader:  teLogoutInfo.DefaultHeader,
		TeLogout:       teLogoutInfo.TeLogout,
		ThisOnlineTime: onlineTime,
	}

	services.GTeService.Track(playerId, teLogoutInfo.GetEvent(), &record, teLogoutInfo.LogoutTime.Unix())
}
