package pubsub

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// consumerWrapper 消费者包装器
type consumerWrapper struct {
	topic   string
	handler MessageHandler
	startFn func(ctx context.Context, handler MessageHandler) error
	closeFn func() error
}

// ConsumerService 消费者服务
type ConsumerService struct {
	consumers []*consumerWrapper
	ctx       context.Context
	cancel    context.CancelFunc
	doneCh    chan struct{}
	options   *ConsumerOptions
	mu        sync.RWMutex
}

// NewConsumerService 创建新的消费者服务
func NewConsumerService(topics []*TopicRegistration, createConsumerFn func(topic string, handler MessageHandler) (func(ctx context.Context, handler MessageHandler) error, func() error, error), opts ...ConsumerOption) (*ConsumerService, error) {
	options := DefaultConsumerOptions()
	for _, opt := range opts {
		opt(options)
	}

	ctx, cancel := context.WithCancel(context.Background())
	cs := &ConsumerService{
		ctx:     ctx,
		cancel:  cancel,
		doneCh:  make(chan struct{}, len(topics)),
		options: options,
	}

	for _, cfg := range topics {
		startFn, closeFn, err := createConsumerFn(cfg.Topic, cfg.Handler)
		if err != nil {
			cancel()
			return nil, fmt.Errorf("failed to create consumer for topic %s: %w", cfg.Topic, err)
		}
		
		wrapper := &consumerWrapper{
			topic:   cfg.Topic,
			handler: cfg.Handler,
			startFn: startFn,
			closeFn: closeFn,
		}
		cs.consumers = append(cs.consumers, wrapper)
	}

	return cs, nil
}

// Start 启动所有消费者
func (cs *ConsumerService) Start(ctx context.Context) error {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	if len(cs.consumers) == 0 {
		return fmt.Errorf("no consumers configured")
	}

	for _, consumer := range cs.consumers {
		go cs.runConsumer(consumer)
	}

	logrus.Infof("Started %d consumers", len(cs.consumers))
	return nil
}

// runConsumer 在goroutine中运行单个消费者
func (cs *ConsumerService) runConsumer(consumer *consumerWrapper) {
	defer func() {
		cs.doneCh <- struct{}{}
		logrus.Infof("Consumer [%s] execution completed", consumer.topic)
	}()

	if err := consumer.startFn(cs.ctx, consumer.handler); err != nil {
		if cs.ctx.Err() == nil {
			logrus.Errorf("Consumer [%s] error: %v", consumer.topic, err)
		} else {
			logrus.Infof("Consumer [%s] stopped due to shutdown signal", consumer.topic)
		}
	}
}

// Stop 优雅停止所有消费者
func (cs *ConsumerService) Stop() error {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	logrus.Info("Starting graceful shutdown of consumer service...")

	// 1. 取消context，停止接收新消息
	cs.cancel()
	logrus.Info("Stop signal sent")

	// 2. 等待所有消费者完成
	cs.waitForConsumersToComplete()

	// 3. 关闭所有消费者
	cs.closeAllConsumers()

	logrus.Info("Consumer service shutdown completed")
	return nil
}

// waitForConsumersToComplete 等待所有消费者完成执行
func (cs *ConsumerService) waitForConsumersToComplete() {
	consumerCount := len(cs.consumers)
	completedCount := 0
	timeout := time.Duration(cs.options.ShutdownTimeout) * time.Second

	logrus.Infof("Waiting for %d consumers to complete execution...", consumerCount)

	for completedCount < consumerCount {
		select {
		case <-cs.doneCh:
			completedCount++
			logrus.Infof("Completed %d/%d consumers", completedCount, consumerCount)
		case <-time.After(timeout):
			logrus.Warnf("Timeout waiting for consumers to complete, %d/%d completed, forcing exit", completedCount, consumerCount)
			return
		}
	}

	logrus.Info("All consumers completed execution")
}

// closeAllConsumers 关闭所有消费者连接
func (cs *ConsumerService) closeAllConsumers() {
	for _, consumer := range cs.consumers {
		if err := consumer.closeFn(); err != nil {
			logrus.Errorf("Failed to close consumer [%s]: %v", consumer.topic, err)
		}
	}
}

// IsRunning 检查服务是否还在运行
func (cs *ConsumerService) IsRunning() bool {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	select {
	case <-cs.ctx.Done():
		return false
	default:
		return true
	}
}

// GetTopicCount 返回配置的主题数量
func (cs *ConsumerService) GetTopicCount() int {
	cs.mu.RLock()
	defer cs.mu.RUnlock()
	return len(cs.consumers)
}
