package pubsub

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"
)

// ExampleUsage 展示如何使用公共库
func ExampleUsage() {
	// 1. 注册主题和处理函数
	topics := []*TopicRegistration{
		RegisterTopic("user_login", func(ctx context.Context, data []byte) error {
			logrus.Infof("处理用户登录: %s", string(data))
			// 模拟处理时间
			time.Sleep(100 * time.Millisecond)
			return nil
		}),
		RegisterTopic("user_logout", func(ctx context.Context, data []byte) error {
			logrus.Infof("处理用户登出: %s", string(data))
			time.Sleep(50 * time.Millisecond)
			return nil
		}),
	}

	// 2. 创建消费者服务
	consumerService, err := NewKafkaConsumerService(
		topics,
		WithShutdownTimeout(10), // 10秒关闭超时
	)
	if err != nil {
		logrus.Fatalf("创建消费者服务失败: %v", err)
	}

	// 3. 启动消费者
	ctx := context.Background()
	if err := consumerService.Start(ctx); err != nil {
		logrus.Fatalf("启动消费者失败: %v", err)
	}

	logrus.Infof("消费者服务已启动，共 %d 个主题", consumerService.GetTopicCount())

	// 4. 初始化生产者
	if err := InitKafkaProducer(); err != nil {
		logrus.Fatalf("初始化生产者失败: %v", err)
	}

	// 5. 获取生产者并发送消息
	producer, err := GetDefaultProducer()
	if err != nil {
		logrus.Fatalf("获取生产者失败: %v", err)
	}

	// 发送测试消息
	testMessages := []struct {
		topic string
		key   string
		data  string
	}{
		{"user_login", "user001", `{"user_id":"001","timestamp":"2024-01-01T10:00:00Z"}`},
		{"user_logout", "user001", `{"user_id":"001","timestamp":"2024-01-01T11:00:00Z"}`},
	}

	for _, msg := range testMessages {
		err := producer.Produce(ctx, msg.topic, msg.key, []byte(msg.data))
		if err != nil {
			logrus.Errorf("发送消息失败: %v", err)
		} else {
			logrus.Infof("消息已发送到主题 %s", msg.topic)
		}
	}

	// 6. 模拟运行一段时间
	time.Sleep(3 * time.Second)

	// 7. 优雅关闭
	logrus.Info("开始关闭服务...")
	
	if err := consumerService.Stop(); err != nil {
		logrus.Errorf("关闭消费者服务失败: %v", err)
	}

	if err := producer.Close(); err != nil {
		logrus.Errorf("关闭生产者失败: %v", err)
	}

	logrus.Info("服务已完全关闭")
}

// ExampleBackwardCompatibility 展示向后兼容性
func ExampleBackwardCompatibility() {
	// 使用旧的函数签名（向后兼容）
	oldStyleHandler := func(data []byte) {
		logrus.Infof("旧式处理函数: %s", string(data))
	}

	// 通过适配器转换
	adaptedHandler := func(ctx context.Context, data []byte) error {
		oldStyleHandler(data)
		return nil
	}

	topics := []*TopicConfig{
		RegisterTopic("legacy_topic", adaptedHandler),
	}

	service, err := NewKafkaConsumerService(topics)
	if err != nil {
		logrus.Fatalf("创建服务失败: %v", err)
	}

	ctx := context.Background()
	service.Start(ctx)
	
	// 运行一段时间后关闭
	time.Sleep(1 * time.Second)
	service.Stop()
}
