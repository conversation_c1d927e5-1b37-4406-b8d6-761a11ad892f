package pubsub

import "context"

// MessageHandler 消息处理函数类型
type MessageHandler func(ctx context.Context, data []byte) error

// TopicConfig 主题配置
type TopicConfig struct {
	Topic   string         `json:"topic"`   // 主题名称
	Handler MessageHandler `json:"-"`       // 消息处理函数
}

// RegisterTopic 注册一个主题配置
func RegisterTopic(topic string, handler MessageHandler) *TopicConfig {
	return &TopicConfig{
		Topic:   topic,
		Handler: handler,
	}
}

// ServiceOptions 服务配置选项
type ServiceOptions struct {
	ShutdownTimeout int // 关闭超时时间（秒）
	BufferSize      int // 缓冲区大小
}

// DefaultServiceOptions 返回默认服务配置
func DefaultServiceOptions() *ServiceOptions {
	return &ServiceOptions{
		ShutdownTimeout: 10,
		BufferSize:      100,
	}
}

// Option 配置函数类型
type Option func(*ServiceOptions)

// WithShutdownTimeout 设置关闭超时时间
func WithShutdownTimeout(timeout int) Option {
	return func(opts *ServiceOptions) {
		opts.ShutdownTimeout = timeout
	}
}

// WithBufferSize 设置缓冲区大小
func WithBufferSize(size int) Option {
	return func(opts *ServiceOptions) {
		opts.BufferSize = size
	}
}
