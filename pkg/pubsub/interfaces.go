package pubsub

import "context"

// MessageHandler 消息处理函数类型
type MessageHandler func(ctx context.Context, data []byte) error

// TopicRegistration 主题注册信息
type TopicRegistration struct {
	Topic   string         `json:"topic"`   // 主题名称
	Handler MessageHandler `json:"-"`       // 消息处理函数
}

// RegisterTopic 注册一个主题配置
func RegisterTopic(topic string, handler MessageHandler) *TopicRegistration {
	return &TopicRegistration{
		Topic:   topic,
		Handler: handler,
	}
}

// ConsumerOptions 消费者服务配置选项
type ConsumerOptions struct {
	ShutdownTimeout int // 关闭超时时间（秒）
	BufferSize      int // 缓冲区大小
}

// DefaultConsumerOptions 返回默认消费者配置
func DefaultConsumerOptions() *ConsumerOptions {
	return &ConsumerOptions{
		ShutdownTimeout: 10,
		BufferSize:      100,
	}
}

// ConsumerOption 消费者配置函数类型
type ConsumerOption func(*ConsumerOptions)

// WithShutdownTimeout 设置关闭超时时间
func WithShutdownTimeout(timeout int) ConsumerOption {
	return func(opts *ConsumerOptions) {
		opts.ShutdownTimeout = timeout
	}
}

// WithBufferSize 设置缓冲区大小
func WithBufferSize(size int) ConsumerOption {
	return func(opts *ConsumerOptions) {
		opts.BufferSize = size
	}
}

// MessageProducer 消息生产者接口
type MessageProducer interface {
	Produce(ctx context.Context, topic string, key string, value []byte) error
	Close() error
}
