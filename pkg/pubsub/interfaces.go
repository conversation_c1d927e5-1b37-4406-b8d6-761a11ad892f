package pubsub

import "context"

// MessageHandler defines the interface for handling messages
type Message<PERSON>andler func(ctx context.Context, data []byte) error

// Producer defines the interface for message producers
type Producer interface {
	Produce(ctx context.Context, topic string, key string, value []byte) error
	Close() error
}

// Consumer defines the interface for message consumers
type Consumer interface {
	Start(ctx context.Context) error
	Stop() error
	IsRunning() bool
}

// ConsumerService defines the interface for managing multiple consumers
type ConsumerService interface {
	Consumer
	AddTopic(topic string, handler MessageHandler) error
	GetTopicCount() int
}

// TopicConfig represents a topic configuration
type TopicConfig struct {
	Topic   string         `json:"topic"`
	Handler MessageHandler `json:"-"`
}

// NewTopicConfig creates a new topic configuration
func NewTopicConfig(topic string, handler MessageHandler) *TopicConfig {
	return &TopicConfig{
		Topic:   topic,
		Handler: handler,
	}
}

// ServiceOptions defines options for creating services
type ServiceOptions struct {
	ShutdownTimeout int // seconds
	BufferSize      int
}

// DefaultServiceOptions returns default service options
func DefaultServiceOptions() *ServiceOptions {
	return &ServiceOptions{
		ShutdownTimeout: 10,
		BufferSize:      100,
	}
}

// Option defines a function type for configuring services
type Option func(*ServiceOptions)

// WithShutdownTimeout sets the shutdown timeout
func WithShutdownTimeout(timeout int) Option {
	return func(opts *ServiceOptions) {
		opts.ShutdownTimeout = timeout
	}
}

// WithBufferSize sets the buffer size for channels
func WithBufferSize(size int) Option {
	return func(opts *ServiceOptions) {
		opts.BufferSize = size
	}
}
