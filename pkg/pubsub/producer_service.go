package pubsub

import (
	"context"
	"fmt"
	"sync"

	"github.com/sirupsen/logrus"
)

// Producer 消息生产者实现
type Producer struct {
	sendFn  func(ctx context.Context, topic string, key string, value []byte) error
	closeFn func() error
	mu      sync.RWMutex
	closed  bool
}

// NewProducer 创建新的生产者
func NewProducer(sendFn func(ctx context.Context, topic string, key string, value []byte) error, closeFn func() error) *Producer {
	return &Producer{
		sendFn:  sendFn,
		closeFn: closeFn,
	}
}

// Produce 发送消息到指定主题
func (p *Producer) Produce(ctx context.Context, topic string, key string, value []byte) error {
	p.mu.RLock()
	defer p.mu.RUnlock()

	if p.closed {
		return fmt.Errorf("producer is closed")
	}

	if topic == "" {
		return fmt.Errorf("topic cannot be empty")
	}

	logrus.Debugf("Sending message to Kafka: Topic=%s, Key=%s, Size=%d bytes", topic, key, len(value))

	err := p.sendFn(ctx, topic, key, value)
	if err != nil {
		return fmt.Errorf("failed to send message to topic %s: %w", topic, err)
	}

	return nil
}

// Close 关闭生产者
func (p *Producer) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.closed {
		return nil
	}

	p.closed = true
	if p.closeFn != nil {
		if err := p.closeFn(); err != nil {
			return fmt.Errorf("failed to close producer: %w", err)
		}
	}

	logrus.Info("Producer closed successfully")
	return nil
}

// ProducerManager 生产者管理器
type ProducerManager struct {
	producers map[string]*Producer
	createFn  func() (*Producer, error)
	mu        sync.RWMutex
}

var (
	defaultManager *ProducerManager
	managerOnce    sync.Once
)

// GetProducerManager 获取默认生产者管理器实例
func GetProducerManager() *ProducerManager {
	managerOnce.Do(func() {
		defaultManager = &ProducerManager{
			producers: make(map[string]*Producer),
		}
	})
	return defaultManager
}

// SetProducerCreateFunc 设置生产者创建函数
func (pm *ProducerManager) SetProducerCreateFunc(createFn func() (*Producer, error)) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.createFn = createFn
}

// GetProducer 获取生产者实例，如果不存在则创建
func (pm *ProducerManager) GetProducer(name string) (*Producer, error) {
	pm.mu.RLock()
	if producer, exists := pm.producers[name]; exists {
		pm.mu.RUnlock()
		return producer, nil
	}
	pm.mu.RUnlock()

	pm.mu.Lock()
	defer pm.mu.Unlock()

	// 双重检查模式
	if producer, exists := pm.producers[name]; exists {
		return producer, nil
	}

	if pm.createFn == nil {
		return nil, fmt.Errorf("producer create function not set")
	}

	producer, err := pm.createFn()
	if err != nil {
		return nil, fmt.Errorf("failed to create producer %s: %w", name, err)
	}

	pm.producers[name] = producer
	logrus.Infof("Created producer: %s", name)
	return producer, nil
}

// CloseAll 关闭所有管理的生产者
func (pm *ProducerManager) CloseAll() error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	var errs []error
	for name, producer := range pm.producers {
		if err := producer.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close producer %s: %w", name, err))
		}
	}

	pm.producers = make(map[string]*Producer)

	if len(errs) > 0 {
		return fmt.Errorf("errors closing producers: %v", errs)
	}

	logrus.Info("All producers closed successfully")
	return nil
}

// GetDefaultProducer 获取默认生产者实例
func GetDefaultProducer() (MessageProducer, error) {
	return GetProducerManager().GetProducer("default")
}

// SetDefaultProducerCreateFunc 设置默认生产者的创建函数
func SetDefaultProducerCreateFunc(createFn func() (*Producer, error)) {
	GetProducerManager().SetProducerCreateFunc(createFn)
}
