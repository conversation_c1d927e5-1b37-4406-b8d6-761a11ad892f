# PubSub Common Library

A high-quality, reusable pub/sub library for microservices architecture, providing abstractions for message producers and consumers with graceful shutdown capabilities.

## Features

- **Interface-driven design** - Easy to test and extend
- **Graceful shutdown** - Proper resource cleanup and message completion
- **Kafka adapter** - Built-in Kafka support with extensible design
- **Producer management** - Singleton pattern with factory support
- **Configurable options** - Timeout, buffer size, and other settings
- **Thread-safe** - Concurrent access protection
- **Comprehensive logging** - Detailed operation tracking

## Quick Start

### Consumer Service

```go
package main

import (
    "context"
    "fancygame/tasrv/pkg/pubsub"
    "github.com/sirupsen/logrus"
)

func main() {
    // Define message handlers
    topics := []*pubsub.TopicConfig{
        pubsub.NewTopicConfig("user_events", func(ctx context.Context, data []byte) error {
            logrus.Infof("Processing user event: %s", string(data))
            return nil
        }),
        pubsub.NewTopicConfig("order_events", func(ctx context.Context, data []byte) error {
            logrus.Infof("Processing order event: %s", string(data))
            return nil
        }),
    }

    // Create consumer service with options
    service, err := pubsub.NewKafkaConsumerService(
        topics,
        pubsub.WithShutdownTimeout(15), // 15 seconds timeout
        pubsub.WithBufferSize(100),     // Buffer size for channels
    )
    if err != nil {
        logrus.Fatalf("Failed to create consumer service: %v", err)
    }

    // Start consuming
    ctx := context.Background()
    if err := service.Start(ctx); err != nil {
        logrus.Fatalf("Failed to start service: %v", err)
    }

    // Graceful shutdown
    defer func() {
        if err := service.Stop(); err != nil {
            logrus.Errorf("Error during shutdown: %v", err)
        }
    }()

    // Keep running...
    select {}
}
```

### Producer Service

```go
package main

import (
    "context"
    "fancygame/tasrv/pkg/pubsub"
    "github.com/sirupsen/logrus"
)

func main() {
    // Initialize producer
    if err := pubsub.InitKafkaProducer(); err != nil {
        logrus.Fatalf("Failed to initialize producer: %v", err)
    }

    // Get producer instance
    producer, err := pubsub.GetDefaultProducer()
    if err != nil {
        logrus.Fatalf("Failed to get producer: %v", err)
    }
    defer producer.Close()

    // Send messages
    ctx := context.Background()
    err = producer.Produce(ctx, "user_events", "user123", []byte(`{"action":"login","user_id":"123"}`))
    if err != nil {
        logrus.Errorf("Failed to send message: %v", err)
    }
}
```

## Architecture

### Core Interfaces

- `Producer` - Message producer interface
- `Consumer` - Message consumer interface  
- `ConsumerService` - Multi-consumer management interface
- `MessageHandler` - Message processing function type

### Implementations

- `BaseConsumerService` - Generic consumer service implementation
- `BaseProducer` - Generic producer implementation
- `KafkaConsumerService` - Kafka-specific consumer service
- `KafkaProducerFactory` - Kafka producer factory

### Adapters

- `KafkaConsumerWrapper` - Adapts Kafka receiver to ConsumerWrapper
- `KafkaProducerSender` - Adapts Kafka sender to ProducerSender

## Configuration Options

```go
// Service options
opts := []pubsub.Option{
    pubsub.WithShutdownTimeout(30), // 30 seconds shutdown timeout
    pubsub.WithBufferSize(200),     // 200 buffer size for channels
}

service, err := pubsub.NewKafkaConsumerService(topics, opts...)
```

## Migration from Legacy Code

### Before (Legacy)
```go
// Old way
topics := []*pubsub.TopicConfig{
    pubsub.NewTopic("topic1", func(data []byte) {
        // handle message
    }),
}
service := pubsub.NewConsumerService(topics)
service.Start(ctx)
service.Stop()
```

### After (Common Library)
```go
// New way with common library
topics := []*pubsub.TopicConfig{
    pubsub.NewTopicConfig("topic1", func(ctx context.Context, data []byte) error {
        // handle message
        return nil
    }),
}
service, _ := pubsub.NewKafkaConsumerService(topics)
service.Start(ctx)
service.Stop()
```

## Best Practices

1. **Always handle errors** from Start/Stop operations
2. **Use context** for cancellation and timeouts
3. **Implement proper shutdown** in your main function
4. **Configure appropriate timeouts** based on your message processing time
5. **Use structured logging** for better observability
6. **Test with mocks** using the provided interfaces

## Testing

The library is designed with interfaces to make testing easy:

```go
// Mock producer for testing
type MockProducer struct{}

func (m *MockProducer) Produce(ctx context.Context, topic, key string, value []byte) error {
    // Mock implementation
    return nil
}

func (m *MockProducer) Close() error {
    return nil
}
```

## Thread Safety

All components are thread-safe and can be used concurrently across multiple goroutines.
