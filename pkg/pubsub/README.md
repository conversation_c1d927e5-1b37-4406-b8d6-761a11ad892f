# PubSub 公共库

为微服务架构设计的高质量、可复用的发布/订阅库，提供消息生产者和消费者的抽象，支持优雅关闭。

## 特性

- **简洁设计** - 避免过度抽象，保持代码简单
- **优雅关闭** - 正确的资源清理和消息完成处理
- **Kafka适配器** - 内置Kafka支持，设计可扩展
- **生产者管理** - 单例模式管理
- **可配置选项** - 超时时间、缓冲区大小等设置
- **线程安全** - 并发访问保护
- **详细日志** - 完整的操作跟踪

## 快速开始

### 消费者服务

```go
package main

import (
    "context"
    "fancygame/tasrv/pkg/pubsub"
    "github.com/sirupsen/logrus"
)

func main() {
    // 定义消息处理函数
    topics := []*pubsub.TopicRegistration{
        pubsub.RegisterTopic("user_events", func(ctx context.Context, data []byte) error {
            logrus.Infof("处理用户事件: %s", string(data))
            return nil
        }),
        pubsub.RegisterTopic("order_events", func(ctx context.Context, data []byte) error {
            logrus.Infof("处理订单事件: %s", string(data))
            return nil
        }),
    }

    // 创建消费者服务并配置选项
    service, err := pubsub.NewKafkaConsumerService(
        topics,
        pubsub.WithShutdownTimeout(15), // 15秒超时
        pubsub.WithBufferSize(100),     // 管道缓冲区大小
    )
    if err != nil {
        logrus.Fatalf("创建消费者服务失败: %v", err)
    }

    // 启动消费
    ctx := context.Background()
    if err := service.Start(ctx); err != nil {
        logrus.Fatalf("启动服务失败: %v", err)
    }

    // 优雅关闭
    defer func() {
        if err := service.Stop(); err != nil {
            logrus.Errorf("关闭过程中出错: %v", err)
        }
    }()

    // 保持运行...
    select {}
}
```

### 生产者服务

```go
package main

import (
    "context"
    "fancygame/tasrv/pkg/pubsub"
    "github.com/sirupsen/logrus"
)

func main() {
    // 初始化生产者
    if err := pubsub.InitKafkaProducer(); err != nil {
        logrus.Fatalf("初始化生产者失败: %v", err)
    }

    // 获取生产者实例
    producer, err := pubsub.GetDefaultProducer()
    if err != nil {
        logrus.Fatalf("获取生产者失败: %v", err)
    }
    defer producer.Close()

    // 发送消息
    ctx := context.Background()
    err = producer.Produce(ctx, "user_events", "user123", []byte(`{"action":"login","user_id":"123"}`))
    if err != nil {
        logrus.Errorf("发送消息失败: %v", err)
    }
}
```

## Architecture

### Core Interfaces

- `Producer` - Message producer interface
- `Consumer` - Message consumer interface  
- `ConsumerService` - Multi-consumer management interface
- `MessageHandler` - Message processing function type

### Implementations

- `BaseConsumerService` - Generic consumer service implementation
- `BaseProducer` - Generic producer implementation
- `KafkaConsumerService` - Kafka-specific consumer service
- `KafkaProducerFactory` - Kafka producer factory

### Adapters

- `KafkaConsumerWrapper` - Adapts Kafka receiver to ConsumerWrapper
- `KafkaProducerSender` - Adapts Kafka sender to ProducerSender

## Configuration Options

```go
// Service options
opts := []pubsub.Option{
    pubsub.WithShutdownTimeout(30), // 30 seconds shutdown timeout
    pubsub.WithBufferSize(200),     // 200 buffer size for channels
}

service, err := pubsub.NewKafkaConsumerService(topics, opts...)
```

## Migration from Legacy Code

### Before (Legacy)
```go
// Old way
topics := []*pubsub.TopicConfig{
    pubsub.NewTopic("topic1", func(data []byte) {
        // handle message
    }),
}
service := pubsub.NewConsumerService(topics)
service.Start(ctx)
service.Stop()
```

### After (Common Library)
```go
// New way with common library
topics := []*pubsub.TopicConfig{
    pubsub.NewTopicConfig("topic1", func(ctx context.Context, data []byte) error {
        // handle message
        return nil
    }),
}
service, _ := pubsub.NewKafkaConsumerService(topics)
service.Start(ctx)
service.Stop()
```

## Best Practices

1. **Always handle errors** from Start/Stop operations
2. **Use context** for cancellation and timeouts
3. **Implement proper shutdown** in your main function
4. **Configure appropriate timeouts** based on your message processing time
5. **Use structured logging** for better observability
6. **Test with mocks** using the provided interfaces

## Testing

The library is designed with interfaces to make testing easy:

```go
// Mock producer for testing
type MockProducer struct{}

func (m *MockProducer) Produce(ctx context.Context, topic, key string, value []byte) error {
    // Mock implementation
    return nil
}

func (m *MockProducer) Close() error {
    return nil
}
```

## Thread Safety

All components are thread-safe and can be used concurrently across multiple goroutines.
