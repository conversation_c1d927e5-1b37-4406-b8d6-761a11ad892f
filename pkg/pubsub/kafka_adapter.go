package pubsub

import (
	"context"
	"fmt"

	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"github.com/sirupsen/logrus"
)

// CreateKafkaConsumer 创建Kafka消费者
func CreateKafkaConsumer(topic string, handler MessageHandler) (func(ctx context.Context, handler MessageHandler) error, func() error, error) {
	receiver, err := event.NewKafkaReceiver(
		event.WithTopic(topic),
	)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create kafka receiver for topic %s: %w", topic, err)
	}

	startFn := func(ctx context.Context, handler MessageHandler) error {
		eventHandler := func(ctx context.Context, evt event.Event) error {
			if err := handler(ctx, evt.Value()); err != nil {
				logrus.Errorf("Handler error for topic [%s], key [%s]: %v", topic, evt.Key(), err)
				return err
			}
			logrus.Tracef("[%s] consume key:%s", topic, evt.Key())
			return nil
		}
		return receiver.SafeReceive(ctx, eventHandler)
	}

	closeFn := func() error {
		return receiver.Close()
	}

	return startFn, closeFn, nil
}

// CreateKafkaProducer 创建Kafka生产者
func CreateKafkaProducer() (*Producer, error) {
	sender, err := event.NewKafkaSender()
	if err != nil {
		return nil, fmt.Errorf("failed to create kafka sender: %w", err)
	}

	sendFn := func(ctx context.Context, topic string, key string, value []byte) error {
		return sender.SendWithTopic(ctx, topic, event.NewMessage(key, value))
	}

	closeFn := func() error {
		// 注意：event.Sender接口可能没有Close方法
		// 这取决于具体实现
		if closer, ok := sender.(interface{ Close() error }); ok {
			return closer.Close()
		}
		return nil
	}

	return NewProducer(sendFn, closeFn), nil
}

// NewKafkaConsumerService 创建新的Kafka消费者服务
func NewKafkaConsumerService(topics []*TopicRegistration, opts ...ConsumerOption) (*ConsumerService, error) {
	return NewConsumerService(topics, CreateKafkaConsumer, opts...)
}

// InitKafkaProducer 初始化默认Kafka生产者
func InitKafkaProducer() error {
	SetDefaultProducerCreateFunc(CreateKafkaProducer)

	// 测试创建生产者以确保工作正常
	_, err := GetDefaultProducer()
	if err != nil {
		return fmt.Errorf("failed to initialize kafka producer: %w", err)
	}

	logrus.Info("Kafka producer initialized successfully")
	return nil
}
