package pubsub

import (
	"context"
	"fmt"

	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"github.com/sirupsen/logrus"
)

// KafkaConsumerWrapper wraps a Kafka receiver to implement ConsumerWrapper
type KafkaConsumerWrapper struct {
	receiver event.Receiver
	handler  MessageHandler
	topic    string
}

// NewKafkaConsumerWrapper creates a new Kafka consumer wrapper
func NewKafkaConsumerWrapper(topic string, handler MessageHandler) (*KafkaConsumerWrapper, error) {
	receiver, err := event.NewKafkaReceiver(
		event.WithTopic(topic),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create kafka receiver for topic %s: %w", topic, err)
	}

	return &KafkaConsumerWrapper{
		receiver: receiver,
		handler:  handler,
		topic:    topic,
	}, nil
}

// Start starts the Kafka consumer
func (kc *KafkaConsumerWrapper) Start(ctx context.Context) error {
	eventHandler := func(ctx context.Context, evt event.Event) error {
		if err := kc.handler(ctx, evt.Value()); err != nil {
			logrus.Errorf("Handler error for topic [%s], key [%s]: %v", kc.topic, evt.Key(), err)
			return err
		}
		logrus.Tracef("[%s] consume key:%s", kc.topic, evt.Key())
		return nil
	}

	return kc.receiver.SafeReceive(ctx, eventHandler)
}

// Close closes the Kafka consumer
func (kc *KafkaConsumerWrapper) Close() error {
	return kc.receiver.Close()
}

// GetTopic returns the topic name
func (kc *KafkaConsumerWrapper) GetTopic() string {
	return kc.topic
}

// KafkaConsumerFactory implements ConsumerFactory for Kafka
type KafkaConsumerFactory struct{}

// CreateConsumer creates a new Kafka consumer
func (f *KafkaConsumerFactory) CreateConsumer(config *TopicConfig) (ConsumerWrapper, error) {
	return NewKafkaConsumerWrapper(config.Topic, config.Handler)
}

// KafkaProducerSender implements ProducerSender for Kafka
type KafkaProducerSender struct {
	sender event.Sender
}

// NewKafkaProducerSender creates a new Kafka producer sender
func NewKafkaProducerSender() (*KafkaProducerSender, error) {
	sender, err := event.NewKafkaSender()
	if err != nil {
		return nil, fmt.Errorf("failed to create kafka sender: %w", err)
	}

	return &KafkaProducerSender{
		sender: sender,
	}, nil
}

// Send sends a message using Kafka sender
func (kps *KafkaProducerSender) Send(ctx context.Context, topic string, key string, value []byte) error {
	return kps.sender.SendWithTopic(ctx, topic, event.NewMessage(key, value))
}

// Close closes the Kafka sender
func (kps *KafkaProducerSender) Close() error {
	// Note: The event.Sender interface might not have a Close method
	// This depends on the actual implementation
	if closer, ok := kps.sender.(interface{ Close() error }); ok {
		return closer.Close()
	}
	return nil
}

// KafkaProducerFactory implements ProducerFactory for Kafka
type KafkaProducerFactory struct{}

// CreateProducer creates a new Kafka producer
func (f *KafkaProducerFactory) CreateProducer() (Producer, error) {
	sender, err := NewKafkaProducerSender()
	if err != nil {
		return nil, err
	}
	return NewBaseProducer(sender), nil
}

// KafkaConsumerService is a Kafka-specific implementation of ConsumerService
type KafkaConsumerService struct {
	*BaseConsumerService
}

// NewKafkaConsumerService creates a new Kafka consumer service
func NewKafkaConsumerService(topics []*TopicConfig, opts ...Option) (*KafkaConsumerService, error) {
	factory := &KafkaConsumerFactory{}
	baseService, err := NewBaseConsumerService(topics, factory, opts...)
	if err != nil {
		return nil, err
	}

	return &KafkaConsumerService{
		BaseConsumerService: baseService,
	}, nil
}

// InitKafkaProducer initializes the default Kafka producer
func InitKafkaProducer() error {
	factory := &KafkaProducerFactory{}
	SetDefaultProducerFactory(factory)
	
	// Test create a producer to ensure it works
	_, err := GetDefaultProducer()
	if err != nil {
		return fmt.Errorf("failed to initialize kafka producer: %w", err)
	}
	
	logrus.Info("Kafka producer initialized successfully")
	return nil
}
