package pubsub

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// consumerWrapper 消费者包装器
type consumerWrapper struct {
	topic   string
	handler MessageHandler
	startFn func(ctx context.Context, handler MessageHandler) error
	closeFn func() error
}

// ConsumerService 消费者服务
type ConsumerService struct {
	consumers []*consumerWrapper
	ctx       context.Context
	cancel    context.CancelFunc
	doneCh    chan struct{}
	options   *ServiceOptions
	mu        sync.RWMutex
}

// NewConsumerService 创建新的消费者服务
func NewConsumerService(topics []*TopicConfig, createConsumerFn func(topic string, handler MessageHandler) (func(ctx context.Context, handler MessageHandler) error, func() error, error), opts ...Option) (*ConsumerService, error) {
	options := DefaultServiceOptions()
	for _, opt := range opts {
		opt(options)
	}

	ctx, cancel := context.WithCancel(context.Background())
	cs := &ConsumerService{
		ctx:     ctx,
		cancel:  cancel,
		doneCh:  make(chan struct{}, len(topics)),
		options: options,
	}

	for _, cfg := range topics {
		startFn, closeFn, err := createConsumerFn(cfg.Topic, cfg.Handler)
		if err != nil {
			cancel()
			return nil, fmt.Errorf("failed to create consumer for topic %s: %w", cfg.Topic, err)
		}

		wrapper := &consumerWrapper{
			topic:   cfg.Topic,
			handler: cfg.Handler,
			startFn: startFn,
			closeFn: closeFn,
		}
		cs.consumers = append(cs.consumers, wrapper)
	}

	return cs, nil
}

// Start starts all consumers
func (cs *BaseConsumerService) Start(ctx context.Context) error {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	if len(cs.consumers) == 0 {
		return fmt.Errorf("no consumers configured")
	}

	for _, consumer := range cs.consumers {
		go cs.runConsumer(consumer)
	}

	logrus.Infof("Started %d consumers", len(cs.consumers))
	return nil
}

// runConsumer runs a single consumer in a goroutine
func (cs *BaseConsumerService) runConsumer(consumer ConsumerWrapper) {
	defer func() {
		cs.doneCh <- struct{}{}
		logrus.Infof("Consumer [%s] execution completed", consumer.GetTopic())
	}()

	if err := consumer.Start(cs.ctx); err != nil {
		if cs.ctx.Err() == nil {
			logrus.Errorf("Consumer [%s] error: %v", consumer.GetTopic(), err)
		} else {
			logrus.Infof("Consumer [%s] stopped due to shutdown signal", consumer.GetTopic())
		}
	}
}

// Stop gracefully stops all consumers
func (cs *BaseConsumerService) Stop() error {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	logrus.Info("Starting graceful shutdown of consumer service...")

	// 1. Cancel context to stop receiving new messages
	cs.cancel()
	logrus.Info("Stop signal sent")

	// 2. Wait for all consumers to complete
	cs.waitForConsumersToComplete()

	// 3. Close all consumers
	cs.closeAllConsumers()

	logrus.Info("Consumer service shutdown completed")
	return nil
}

// waitForConsumersToComplete waits for all consumers to complete execution
func (cs *BaseConsumerService) waitForConsumersToComplete() {
	consumerCount := len(cs.consumers)
	completedCount := 0
	timeout := time.Duration(cs.options.ShutdownTimeout) * time.Second

	logrus.Infof("Waiting for %d consumers to complete execution...", consumerCount)

	for completedCount < consumerCount {
		select {
		case <-cs.doneCh:
			completedCount++
			logrus.Infof("Completed %d/%d consumers", completedCount, consumerCount)
		case <-time.After(timeout):
			logrus.Warnf("Timeout waiting for consumers to complete, %d/%d completed, forcing exit", completedCount, consumerCount)
			return
		}
	}

	logrus.Info("All consumers completed execution")
}

// closeAllConsumers closes all consumer connections
func (cs *BaseConsumerService) closeAllConsumers() {
	for _, consumer := range cs.consumers {
		if err := consumer.Close(); err != nil {
			logrus.Errorf("Failed to close consumer [%s]: %v", consumer.GetTopic(), err)
		}
	}
}

// IsRunning checks if the service is still running
func (cs *BaseConsumerService) IsRunning() bool {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	select {
	case <-cs.ctx.Done():
		return false
	default:
		return true
	}
}

// AddTopic adds a new topic configuration (not implemented in base class)
func (cs *BaseConsumerService) AddTopic(topic string, handler MessageHandler) error {
	return fmt.Errorf("AddTopic not implemented in base consumer service")
}

// GetTopicCount returns the number of configured topics
func (cs *BaseConsumerService) GetTopicCount() int {
	cs.mu.RLock()
	defer cs.mu.RUnlock()
	return len(cs.consumers)
}

// ConsumerFactory defines the interface for creating consumers
type ConsumerFactory interface {
	CreateConsumer(config *TopicConfig) (ConsumerWrapper, error)
}
