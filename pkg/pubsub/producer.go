package pubsub

import (
	"context"
	"fmt"
	"sync"

	"github.com/sirupsen/logrus"
)

// BaseProducer provides a base implementation for message producers
type BaseProducer struct {
	sender ProducerSender
	mu     sync.RWMutex
	closed bool
}

// ProducerSender defines the interface for sending messages
type ProducerSender interface {
	Send(ctx context.Context, topic string, key string, value []byte) error
	Close() error
}

// NewBaseProducer creates a new base producer
func NewBaseProducer(sender ProducerSender) *BaseProducer {
	return &BaseProducer{
		sender: sender,
	}
}

// Produce sends a message to the specified topic
func (p *BaseProducer) Produce(ctx context.Context, topic string, key string, value []byte) error {
	p.mu.RLock()
	defer p.mu.RUnlock()

	if p.closed {
		return fmt.Errorf("producer is closed")
	}

	if topic == "" {
		return fmt.Errorf("topic cannot be empty")
	}

	logrus.Debugf("Sending message to Kafka: Topic=%s, Key=%s, Size=%d bytes", topic, key, len(value))

	err := p.sender.Send(ctx, topic, key, value)
	if err != nil {
		return fmt.Errorf("failed to send message to topic %s: %w", topic, err)
	}

	return nil
}

// Close closes the producer
func (p *BaseProducer) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.closed {
		return nil
	}

	p.closed = true
	if err := p.sender.Close(); err != nil {
		return fmt.Errorf("failed to close producer sender: %w", err)
	}

	logrus.Info("Producer closed successfully")
	return nil
}

// ProducerManager manages producer instances with singleton pattern
type ProducerManager struct {
	producers map[string]Producer
	factory   ProducerFactory
	mu        sync.RWMutex
	once      sync.Once
}

// ProducerFactory defines the interface for creating producers
type ProducerFactory interface {
	CreateProducer() (Producer, error)
}

var (
	defaultManager *ProducerManager
	managerOnce    sync.Once
)

// GetProducerManager returns the default producer manager instance
func GetProducerManager() *ProducerManager {
	managerOnce.Do(func() {
		defaultManager = &ProducerManager{
			producers: make(map[string]Producer),
		}
	})
	return defaultManager
}

// SetProducerFactory sets the producer factory for the manager
func (pm *ProducerManager) SetProducerFactory(factory ProducerFactory) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.factory = factory
}

// GetProducer returns a producer instance, creating one if necessary
func (pm *ProducerManager) GetProducer(name string) (Producer, error) {
	pm.mu.RLock()
	if producer, exists := pm.producers[name]; exists {
		pm.mu.RUnlock()
		return producer, nil
	}
	pm.mu.RUnlock()

	pm.mu.Lock()
	defer pm.mu.Unlock()

	// Double-check pattern
	if producer, exists := pm.producers[name]; exists {
		return producer, nil
	}

	if pm.factory == nil {
		return nil, fmt.Errorf("producer factory not set")
	}

	producer, err := pm.factory.CreateProducer()
	if err != nil {
		return nil, fmt.Errorf("failed to create producer %s: %w", name, err)
	}

	pm.producers[name] = producer
	logrus.Infof("Created producer: %s", name)
	return producer, nil
}

// CloseAll closes all managed producers
func (pm *ProducerManager) CloseAll() error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	var errs []error
	for name, producer := range pm.producers {
		if err := producer.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close producer %s: %w", name, err))
		}
	}

	pm.producers = make(map[string]Producer)

	if len(errs) > 0 {
		return fmt.Errorf("errors closing producers: %v", errs)
	}

	logrus.Info("All producers closed successfully")
	return nil
}

// GetDefaultProducer returns the default producer instance
func GetDefaultProducer() (Producer, error) {
	return GetProducerManager().GetProducer("default")
}

// SetDefaultProducerFactory sets the factory for the default producer
func SetDefaultProducerFactory(factory ProducerFactory) {
	GetProducerManager().SetProducerFactory(factory)
}
