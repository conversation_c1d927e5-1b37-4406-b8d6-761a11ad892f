package pubsub

import (
	"context"
	"fmt"
	"sync"

	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"github.com/sirupsen/logrus"
)

// MessageProducer 消息生产者接口
type MessageProducer interface {
	Produce(ctx context.Context, topic string, key string, value []byte) error
	Close() error
}

// Producer Kafka生产者实现
type Producer struct {
	sender event.Sender
	mu     sync.RWMutex
	closed bool
}

var (
	defaultProducer *Producer
	producerOnce    sync.Once
)

// InitKafkaProducer 初始化Kafka生产者
func InitKafkaProducer() error {
	var err error
	producerOnce.Do(func() {
		sender, createErr := event.NewKafkaSender()
		if createErr != nil {
			err = fmt.Errorf("failed to create kafka sender: %w", createErr)
			return
		}
		defaultProducer = &Producer{sender: sender}
		logrus.Info("Kafka producer initialized successfully")
	})
	return err
}

// GetDefaultProducer 获取默认生产者
func GetDefaultProducer() (MessageProducer, error) {
	if defaultProducer == nil {
		return nil, fmt.Errorf("producer not initialized, call InitKafkaProducer first")
	}
	return defaultProducer, nil
}

// Produce 发送消息
func (p *Producer) Produce(ctx context.Context, topic string, key string, value []byte) error {
	p.mu.RLock()
	defer p.mu.RUnlock()

	if p.closed {
		return fmt.Errorf("producer is closed")
	}

	if topic == "" {
		return fmt.Errorf("topic cannot be empty")
	}

	logrus.Debugf("Sending message to Kafka: Topic=%s, Key=%s, Size=%d bytes", topic, key, len(value))

	err := p.sender.SendWithTopic(ctx, topic, event.NewMessage(key, value))
	if err != nil {
		return fmt.Errorf("failed to send message to topic %s: %w", topic, err)
	}

	return nil
}

// Close 关闭生产者
func (p *Producer) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.closed {
		return nil
	}

	p.closed = true
	// 注意：event.Sender可能没有Close方法
	if closer, ok := p.sender.(interface{ Close() error }); ok {
		if err := closer.Close(); err != nil {
			return fmt.Errorf("failed to close producer: %w", err)
		}
	}

	logrus.Info("Producer closed successfully")
	return nil
}
