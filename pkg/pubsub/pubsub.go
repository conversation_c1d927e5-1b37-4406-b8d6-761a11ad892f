package pubsub

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"github.com/sirupsen/logrus"
)

// MessageHandler 消息处理函数类型
type MessageHandler func(ctx context.Context, data []byte) error

// MessageProducer 消息生产者接口
type MessageProducer interface {
	Produce(ctx context.Context, topic string, key string, value []byte) error
	Close() error
}

// TopicRegistration 主题注册信息
type TopicRegistration struct {
	Topic   string         `json:"topic"`
	Handler MessageHandler `json:"-"`
}

// RegisterTopic 注册主题
func RegisterTopic(topic string, handler MessageHandler) *TopicRegistration {
	return &TopicRegistration{
		Topic:   topic,
		Handler: handler,
	}
}

// ConsumerService 消费者服务
type ConsumerService struct {
	topics  []*TopicRegistration
	ctx     context.Context
	cancel  context.CancelFunc
	doneCh  chan struct{}
	timeout time.Duration
	mu      sync.RWMutex
}

// NewKafkaConsumerService 创建Kafka消费者服务
func NewKafkaConsumerService(topics []*TopicRegistration, shutdownTimeout ...time.Duration) *ConsumerService {
	timeout := 10 * time.Second
	if len(shutdownTimeout) > 0 {
		timeout = shutdownTimeout[0]
	}

	ctx, cancel := context.WithCancel(context.Background())
	return &ConsumerService{
		topics:  topics,
		ctx:     ctx,
		cancel:  cancel,
		doneCh:  make(chan struct{}, len(topics)),
		timeout: timeout,
	}
}

// Start 启动消费者服务
func (cs *ConsumerService) Start(ctx context.Context) error {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	if len(cs.topics) == 0 {
		return fmt.Errorf("no topics configured")
	}

	for _, topic := range cs.topics {
		go cs.runConsumer(topic)
	}

	logrus.Infof("Started %d kafka consumers", len(cs.topics))
	return nil
}

// runConsumer 运行单个消费者
func (cs *ConsumerService) runConsumer(topic *TopicRegistration) {
	defer func() {
		cs.doneCh <- struct{}{}
		logrus.Infof("Consumer [%s] execution completed", topic.Topic)
	}()

	// 创建Kafka接收器
	receiver, err := event.NewKafkaReceiver(event.WithTopic(topic.Topic))
	if err != nil {
		logrus.Errorf("Failed to create receiver for topic [%s]: %v", topic.Topic, err)
		return
	}
	defer receiver.Close()

	// 事件处理器
	eventHandler := func(ctx context.Context, evt event.Event) error {
		if err := topic.Handler(ctx, evt.Value()); err != nil {
			logrus.Errorf("Handler error for topic [%s], key [%s]: %v", topic.Topic, evt.Key(), err)
			return err
		}
		logrus.Tracef("[%s] consume key:%s", topic.Topic, evt.Key())
		return nil
	}

	// 开始接收消息
	if err := receiver.SafeReceive(cs.ctx, eventHandler); err != nil {
		if cs.ctx.Err() == nil {
			logrus.Errorf("Consumer [%s] error: %v", topic.Topic, err)
		} else {
			logrus.Infof("Consumer [%s] stopped due to shutdown signal", topic.Topic)
		}
	}
}

// Stop 停止消费者服务
func (cs *ConsumerService) Stop() error {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	logrus.Info("Starting graceful shutdown of consumer service...")

	// 取消context
	cs.cancel()
	logrus.Info("Stop signal sent")

	// 等待所有消费者完成
	consumerCount := len(cs.topics)
	completedCount := 0

	logrus.Infof("Waiting for %d consumers to complete execution...", consumerCount)

	for completedCount < consumerCount {
		select {
		case <-cs.doneCh:
			completedCount++
			logrus.Infof("Completed %d/%d consumers", completedCount, consumerCount)
		case <-time.After(cs.timeout):
			logrus.Warnf("Timeout waiting for consumers to complete, %d/%d completed, forcing exit", completedCount, consumerCount)
			goto done
		}
	}

	logrus.Info("All consumers completed execution")

done:
	logrus.Info("Consumer service shutdown completed")
	return nil
}

// IsRunning 检查服务是否运行中
func (cs *ConsumerService) IsRunning() bool {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	select {
	case <-cs.ctx.Done():
		return false
	default:
		return true
	}
}

// GetTopicCount 获取主题数量
func (cs *ConsumerService) GetTopicCount() int {
	cs.mu.RLock()
	defer cs.mu.RUnlock()
	return len(cs.topics)
}

// Producer Kafka生产者实现
type Producer struct {
	sender event.Sender
	mu     sync.RWMutex
	closed bool
}

var (
	defaultProducer *Producer
	producerOnce    sync.Once
)

// InitKafkaProducer 初始化Kafka生产者
func InitKafkaProducer() error {
	var err error
	producerOnce.Do(func() {
		sender, createErr := event.NewKafkaSender()
		if createErr != nil {
			err = fmt.Errorf("failed to create kafka sender: %w", createErr)
			return
		}
		defaultProducer = &Producer{sender: sender}
		logrus.Info("Kafka producer initialized successfully")
	})
	return err
}

// GetDefaultProducer 获取默认生产者
func GetDefaultProducer() (MessageProducer, error) {
	if defaultProducer == nil {
		return nil, fmt.Errorf("producer not initialized, call InitKafkaProducer first")
	}
	return defaultProducer, nil
}

// Produce 发送消息
func (p *Producer) Produce(ctx context.Context, topic string, key string, value []byte) error {
	p.mu.RLock()
	defer p.mu.RUnlock()

	if p.closed {
		return fmt.Errorf("producer is closed")
	}

	if topic == "" {
		return fmt.Errorf("topic cannot be empty")
	}

	logrus.Debugf("Sending message to Kafka: Topic=%s, Key=%s, Size=%d bytes", topic, key, len(value))

	err := p.sender.SendWithTopic(ctx, topic, event.NewMessage(key, value))
	if err != nil {
		return fmt.Errorf("failed to send message to topic %s: %w", topic, err)
	}

	return nil
}

// Close 关闭生产者
func (p *Producer) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.closed {
		return nil
	}

	p.closed = true
	// 注意：event.Sender可能没有Close方法
	if closer, ok := p.sender.(interface{ Close() error }); ok {
		if err := closer.Close(); err != nil {
			return fmt.Errorf("failed to close producer: %w", err)
		}
	}

	logrus.Info("Producer closed successfully")
	return nil
}
