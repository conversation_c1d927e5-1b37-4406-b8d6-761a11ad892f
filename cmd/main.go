package main

import (
	"context"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/driver"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"runtime"
	"tasrv/internal/config"
	"tasrv/internal/logic"
	"tasrv/internal/proc"
	"tasrv/internal/pubsub"
	"tasrv/internal/services"
)

type taService struct {
	Name string
	Ctx  context.Context
	Cs   *pubsub.ConsumerService
}

func (s *taService) Init() error {
	s.Ctx = context.Background()
	s.Name = viper.GetString(dict.ConfigRpcServerName)

	// 初始化配置
	err := config.InitConfig()
	if err != nil {
		logrus.Errorf("初始化配置失败:%+v", err)
		return err
	}

	logrus.Infoln(s.Name + "服务Init")
	services.Init()
	pubsub.InitProducer()

	// 初始化上报服务
	var topics = []*pubsub.TopicConfig{
		// 用户登录信息上报
		pubsub.NewTopic("aaaaaaaaaaaaaaaaaaaaa", logic.ProcessLoginMessageTest),
		//// 用户注册信息上报
		//pubsub.NewTopic(analyze.EventRegister, logic.ProcessRegisterMessage),
		//// 用户登出信息上报
		//pubsub.NewTopic(analyze.EventLogout, logic.ProcessLogoutMessage),
		//// 道具信息上报
		//pubsub.NewTopic(analyze.EventItem, item.ProcessItemMessage),
	}
	s.Cs = pubsub.NewConsumerService(topics)

	// 初始化gin
	envMode := viper.GetString(dict.ConfigGinEnvMode)
	gin.SetMode(envMode)
	services.RegisterHttpRouter()

	// 初始化客户端协议
	proc.RegClientMsgHandler()

	return nil
}

func (s *taService) Start() error {
	// 这里实现启动逻辑
	services.StartShuShu()
	s.Cs.Start(s.Ctx)
	logrus.Infoln(s.Name + "服务启动成功")
	return nil
}

func (s *taService) Stop() error {
	// 这里实现服务正常关闭逻辑
	logrus.Infoln(s.Name + "服务开始关闭...")

	// 优雅关闭消费者服务
	if s.Cs != nil {
		s.Cs.Stop()
	}

	logrus.Infoln(s.Name + "服务关闭完成")
	return nil
}

func (s *taService) ForceStop() error {
	// 这里实现强制关闭逻辑
	logrus.Infoln(s.Name + " ForceStop ...")
	return nil
}

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU() * 2)
	random.NewInitSource() // 初始化随机数
	driver.Run(&taService{})
}
