# server
rpc_server_name: ta
rpc_port: 14001

# 端口号
http_port: 24001

# 日志相关
log_level: trace
log_write: true
log_dir: ../../logs/ta
log_json: false

redis_list:
  player:
    addr:  192.168.1.58:6379
    passwd: 8888

consul_addr: 192.168.1.58:8500

rpc_server_tags: debug

kafka-consumer:
  brokers: ["192.168.1.58:9092"]
  group: "tasrv"
  timeout: 10

kafka-producer:
  brokers: ["192.168.1.58:9092"]
  timeout: 10

# 数数上报开关
report_switch:
  track: true
  user_set: true
  user_set_once: true
